import {
  createApiRef,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@backstage/core-plugin-api';

export const tunnelsApiRef = createApiRef<TunnelsApi>({
  id: 'plugin.devops-console.tunnels',
});

// 盾山配置记录类型定义
export interface TunnelRecord {
  /** 集群 */
  cluster?: string;
  /** 命名空间 */
  namespace?: string;
  /** 应用名称 */
  app?: string;
  /** 端口 */
  port?: string;
  /** 节点端口 */
  nodePort?: string;
  /** 内部地址 */
  innerAddress?: string;
  /** 外部地址 */
  outerAddress?: string;
  /** 方向 */
  direction?: string;
  /** 隧道名称 */
  tunnelName?: string;
  /** 描述 */
  description?: string;
  /** 本地地址 */
  local?: string;
  /** 隧道地址 */
  tunnel?: string;
  /** 目标地址 */
  target?: string;
  /** 目标地址详情 */
  targetAddress?: string;
  /** 代理配置 */
  proxy?: string;
  /** 连接超时时间 */
  connectTimeout?: string | number;
  /** 读取超时时间 */
  readTimeout?: string | number;
  /** 连接丢失超时时间 */
  connectionLostTimeout?: string | number;
  /** 读取字节数/秒 */
  readBytesPerSecond?: number;
  /** 写入字节数/秒 */
  writeBytesPerSecond?: number;
}

export interface TunnelsApi {
  /** 获取盾山配置列表 */
  getTunnels(useCache?: boolean): Promise<TunnelRecord[]>;
}

export class TunnelsApiClient implements TunnelsApi {
  constructor(
    private readonly discoveryApi: DiscoveryApi,
    private readonly fetchApi: FetchApi,
  ) { }

  async getTunnels(useCache: boolean = true): Promise<TunnelRecord[]> {
    try {
      // 构建请求URL，添加cache参数
      const proxyUrl = await this.discoveryApi.getBaseUrl('proxy');
      const url = `${proxyUrl}/fs-idp-helper/tunnels?cache=${useCache}`;

      const response = await this.fetchApi.fetch(url);

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // 假设API返回的是一个数组，如果是包装对象需要调整
      return Array.isArray(data) ? data : data.data || [];
    } catch (error) {
      throw new Error(`Failed to fetch tunnels: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

export const createTunnelsApiClient = (options: {
  discoveryApi: DiscoveryApi;
  fetchApi: FetchApi;
}): TunnelsApi => {
  return new TunnelsApiClient(options.discoveryApi, options.fetchApi);
};
