import { SettingsLayout } from '@backstage/plugin-user-settings';
import { OverviewTab } from '../OverviewTab';
import { SmsQueryTab } from '../SmsQueryTab';
import { TunnelsQueryTab } from '../TunnelsQueryTab';
import { usePermission } from '@backstage/plugin-permission-react';
import { smsPermissions, tunnelPermissions } from '@internal/plugin-devops-console-common';

export const DevopsConsoleComponent = () => {
  const { loading: loadingPermission, allowed: canReadSms } = usePermission({
    permission: smsPermissions,
  });
  const { loading: loadingTunnelsPermission, allowed: canReadTunnels } = usePermission({
    permission: tunnelPermissions,
  });

  return (
    <SettingsLayout title='研发运维平台'>
      <SettingsLayout.Route path="overview" title="概览">
        <OverviewTab />
      </SettingsLayout.Route>
      {!loadingPermission && canReadSms && (
        <SettingsLayout.Route path="sms" title="短信查询">
          <SmsQueryTab />
        </SettingsLayout.Route>
      )}
      {!loadingTunnelsPermission && canReadTunnels && (
        <SettingsLayout.Route path="tunnels" title="盾山查询">
          <TunnelsQueryTab />
        </SettingsLayout.Route>
      )}
      {/* <SettingsLayout.Route path="dynamic-table" title="动态表格">
        <DynamicTableDemo />
      </SettingsLayout.Route> */}
    </SettingsLayout>
  );
};

