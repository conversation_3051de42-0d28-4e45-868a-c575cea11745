import { useState, useRef } from 'react';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { Typography, IconButton, Tooltip } from '@material-ui/core';
import RefreshIcon from '@material-ui/icons/Refresh';

import { TunnelRecord } from '../../api/TunnelsApi';
import { JsonViewerDialog } from '../JsonViewer';

type TunnelsTableProps = {
  tunnelRecords: TunnelRecord[];
  loading: boolean;
  error?: Error;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  initialSearchText?: string;
};

export const TunnelsTable = ({
  tunnelRecords,
  loading,
  error,
  onSearch,
  onRefresh,
  initialSearchText = '',
}: TunnelsTableProps) => {
  // 用于延迟搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于存储选中行的数据和对话框状态
  const [selectedRow, setSelectedRow] = useState<TunnelRecord | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理搜索输入变化，使用防抖
  const handleSearchChange = (searchText: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，延迟1秒触发搜索
    if (onSearch) {
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(searchText);
      }, 1000);
    }
  };

  // 处理行双击事件
  const handleRowClick = (_event?: any, rowData?: TunnelRecord) => {
    if (rowData) {
      setSelectedRow(rowData);
      setDialogOpen(true);
    }
  };

  // 处理对话框关闭
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRow(null);
  };

  // 安全渲染函数 - 确保值始终是字符串或React元素
  const safeRender = (value: any): React.ReactNode => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return String(value);
      }
    }
    return String(value);
  };

  // 带截断的安全渲染函数
  const safeRenderWithTruncate = (value: any, maxLength: number): React.ReactNode => {
    if (value === null || value === undefined) return '-';

    let strValue: string;
    if (typeof value === 'object') {
      try {
        strValue = JSON.stringify(value);
      } catch {
        strValue = String(value);
      }
    } else {
      strValue = String(value);
    }

    if (strValue.length > maxLength) {
      return (
        <Tooltip title={strValue}>
          <span>{strValue.substring(0, maxLength)}...</span>
        </Tooltip>
      );
    }
    return strValue;
  };

  // 固定列配置
  const columns: TableColumn[] = [
    {
      title: '集群',
      field: 'cluster',
      // render: (value: any) => safeRender(value)
    },
    {
      title: '命名空间',
      field: 'namespace',
      // render: (value: any) => safeRender(value)
    },
    {
      title: '应用名称',
      field: 'app',
      render: (value: any) => safeRender(value)
    },
    {
      title: '名称',
      field: 'name',
      render: (value: any) => safeRender(value)
    },
    {
      title: '端口',
      field: 'port',
      render: (value: any) => safeRender(value)
    },
    {
      title: '节点端口',
      field: 'nodePort',
      render: (value: any) => safeRender(value)
    },
    {
      title: '集群内部地址',
      field: 'clusterInnerAddress',
      render: (value: any) => safeRenderWithTruncate(value, 30)
    },
    {
      title: '集群外部地址',
      field: 'clusterOuterAddress',
      render: (value: any) => safeRenderWithTruncate(value, 30)
    },
    {
      title: '内部地址',
      field: 'innerAddress',
      render: (value: any) => safeRenderWithTruncate(value, 30)
    },
    {
      title: '外部地址',
      field: 'outerAddress',
      render: (value: any) => safeRenderWithTruncate(value, 30)
    },
  ];

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return (
    <>
      <Table
        title="盾山查询"
        isLoading={loading}
        options={{
          search: true,
          paging: true,
          pageSize: 20,
          pageSizeOptions: [10, 20, 50, 100, 10000],
          padding: 'dense',
          showFirstLastPageButtons: true,
          searchText: initialSearchText,
          searchAutoFocus: true,
          loadingType: 'linear',
        }}
        localization={{
          toolbar: {
            searchPlaceholder: '全字段搜索...',
          },
        }}
        data={tunnelRecords}
        columns={columns}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            没有找到匹配的记录
          </Typography>
        }
        actions={[
          {
            icon: () => (
              <Tooltip title="刷新数据">
                <IconButton size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            ),
            tooltip: '刷新',
            isFreeAction: true,
            onClick: () => onRefresh && onRefresh(),
          },
        ]}
      />

      {/* 详情对话框 */}
      <JsonViewerDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        title="盾山配置 - 详细信息"
        data={selectedRow}
        maxWidth="md"
      />
    </>
  );
};
