import { useState, useRef } from 'react';
import {
  Table,
  TableColumn,
  ResponseErrorPanel,
} from '@backstage/core-components';
import { Typography, IconButton, Tooltip } from '@material-ui/core';
import RefreshIcon from '@material-ui/icons/Refresh';

import { TunnelRecord } from '../../api/TunnelsApi';
import { JsonViewerDialog } from '../JsonViewer';


type TunnelsTableProps = {
  tunnelRecords: TunnelRecord[];
  loading: boolean;
  error?: Error;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  initialSearchText?: string;
};

export const TunnelsTable = ({
  tunnelRecords,
  loading,
  error,
  onSearch,
  onRefresh,
  initialSearchText = '',
}: TunnelsTableProps) => {
  // 用于延迟搜索的定时器引用
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于存储选中行的数据和对话框状态
  const [selectedRow, setSelectedRow] = useState<TunnelRecord | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 处理搜索输入变化，使用防抖
  const handleSearchChange = (searchText: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，延迟1秒触发搜索
    if (onSearch) {
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(searchText);
      }, 1000);
    }
  };

  // 处理行双击事件
  const handleRowClick = (_event?: any, rowData?: TunnelRecord) => {
    if (rowData) {
      setSelectedRow(rowData);
      setDialogOpen(true);
    }
  };

  // 处理对话框关闭
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRow(null);
  };

  const columns: TableColumn[] = [
    {
      title: '名称',
      field: 'tunnelName',
      width: '10%',
    },
    {
      title: '描述',
      field: 'description',
      width: '20%',
      render: (row: any) => {
        return (
          <Tooltip title={`${row.direction} ${row.description}`}>
            <span>{row.description}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '运行环境',
      width: '20%',
      render: (row: any) => {
        return (
          <Tooltip title={`${row.cluster} ${row.namespace} ${row.app}`}>
            <span>{`${row.namespace}/${row.app}`}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '集群内地址',
      field: 'innerAddress',
      width: '20%',
    },
    {
      title: '集群外地址',
      field: 'outerAddress',
      width: '15%',
    },
    {
      title: '代理目的地址',
      field: 'targetAddress',
      width: '15%',
      render: (row: any) => {
        return (
          <Tooltip title={`${row.target} ${row.targetAddress}`}>
            <span>{row.targetAddress}</span>
          </Tooltip>
        );
      },
    },
  ];

  if (error) {
    return <ResponseErrorPanel error={error} />;
  }

  return (
    <>
      <Table
        title="盾山查询"
        isLoading={loading}
        options={{
          search: true,
          paging: true,
          pageSize: 20,
          pageSizeOptions: [10, 20, 50, 100, 1000],
          padding: 'dense',
          showFirstLastPageButtons: true,
          searchText: initialSearchText,
          searchAutoFocus: true,
          loadingType: 'linear',
          tableLayout: 'auto',
        }}
        localization={{
          toolbar: {
            searchPlaceholder: '全字段搜索...',
          },
        }}
        data={tunnelRecords}
        columns={columns}
        onSearchChange={handleSearchChange}
        onDoubleRowClick={handleRowClick}
        emptyContent={
          <Typography variant="body1" style={{ padding: '16px' }}>
            没有找到匹配的记录
          </Typography>
        }
        actions={[
          {
            icon: () => (
              <Tooltip title="刷新数据">
                <IconButton size="small">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            ),
            isFreeAction: true,
            onClick: () => onRefresh && onRefresh(),
          },
        ]}
      />

      {/* 详情对话框 */}
      <JsonViewerDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        title="盾山配置详细信息"
        data={selectedRow}
        maxWidth="md"
      />
    </>
  );
};
