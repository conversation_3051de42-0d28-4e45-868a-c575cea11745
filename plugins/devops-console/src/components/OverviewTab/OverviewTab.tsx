import { Typography, Grid, Card, CardContent, Box, Chip } from '@material-ui/core';
import { InfoCard } from '@backstage/core-components';
import { makeStyles } from '@material-ui/core/styles';
import SmsIcon from '@material-ui/icons/Sms';
import SecurityIcon from '@material-ui/icons/Security';
import TableChartIcon from '@material-ui/icons/TableChart';
import InfoIcon from '@material-ui/icons/Info';

const useStyles = makeStyles((theme) => ({
  featureCard: {
    height: '100%',
    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    },
  },
  featureIcon: {
    fontSize: '2.5rem',
    color: theme.palette.primary.main,
    marginBottom: theme.spacing(1),
  },
  featureTitle: {
    fontWeight: 600,
    marginBottom: theme.spacing(1),
  },
  featureDescription: {
    color: theme.palette.text.secondary,
    lineHeight: 1.6,
  },
  statusChip: {
    marginTop: theme.spacing(1),
  },
  availableChip: {
    backgroundColor: theme.palette.success.light,
    color: theme.palette.success.contrastText,
  },
  comingSoonChip: {
    backgroundColor: theme.palette.warning.light,
    color: theme.palette.warning.contrastText,
  },
}));

export const OverviewTab = () => {
  const classes = useStyles();

  const features = [
    {
      title: '短信查询',
      description: '查询短信发送历史记录，支持按手机号搜索，查看发送状态和详细信息。提供完整的短信发送日志和状态跟踪。',
      icon: <SmsIcon className={classes.featureIcon} />,
      status: 'available',
      statusText: '可用',
    },
    {
      title: '盾山查询',
      description: '查询盾山隧道配置信息，包括集群内外地址、代理配置等。支持全字段搜索和详细信息查看。',
      icon: <SecurityIcon className={classes.featureIcon} />,
      status: 'available',
      statusText: '可用',
    },
    {
      title: '动态表格',
      description: '可配置的动态表格组件，支持自定义API端点、分页、过滤和排序。适用于各种数据查询场景。',
      icon: <TableChartIcon className={classes.featureIcon} />,
      status: 'coming-soon',
      statusText: '即将推出',
    },
  ];

  return (
    <Grid container spacing={3} direction="column">
      <Grid item>
        <InfoCard title="研发运维平台" icon={<InfoIcon />}>
          <Typography variant="body1" paragraph>
            欢迎使用研发运维平台！这里提供了一系列实用的运维工具和查询功能，帮助您更好地管理和监控系统。
          </Typography>
          <Typography variant="body2" color="textSecondary">
            如需访问特定功能的权限，请联系管理员申请。部分功能可能需要相应的权限才能使用。
          </Typography>
        </InfoCard>
      </Grid>

      <Grid item>
        <Typography variant="h6" gutterBottom style={{ marginBottom: '16px' }}>
          功能模块
        </Typography>
        <Grid container spacing={3}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card className={classes.featureCard}>
                <CardContent>
                  <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                    {feature.icon}
                    <Typography variant="h6" className={classes.featureTitle}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" className={classes.featureDescription}>
                      {feature.description}
                    </Typography>
                    <Chip
                      label={feature.statusText}
                      size="small"
                      className={`${classes.statusChip} ${feature.status === 'available' ? classes.availableChip : classes.comingSoonChip
                        }`}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Grid>
    </Grid>
  );
};
