# 由oauth2-proxy对外暴露ingress，参考 https://git.firstshare.cn/devops/fs-platform-engineering-installer.git

global:
#   imageRegistry: reg.firstshare.cn/docker.io
  storageClass: "managed-nfs-storage"
  security:
    allowInsecureImages: true

ingress:
  enabled: false

backstage:
  image:
    registry: reg.firstshare.cn
    repository: app/fs-idp-backstage
    tag: main
    # pullPolicy: IfNotPresent
    pullPolicy: Always

  extraEnvVars:
    # Node.js 忽略 https 证书校验
    - name: NODE_TLS_REJECT_UNAUTHORIZED
      value: "0"
    - name: ADMIN_TOKEN
      value: "EVep4hLrKDlvXaTMDwnrsMal6nA6lecspjAF+SaaOFcSylnv0ZZN+FJd"

  appConfig:
    app:
      title: 开发者平台
      baseUrl: https://dash.firstshare.cn
    organization:
      name: 纷享

    backend:
      baseUrl: https://dash.firstshare.cn
      database:
        client: pg
        # 使用单个数据库，通过schema（默认是database）区分不同插件的表，参考 https://backstage.io/docs/tutorials/switching-sqlite-postgres/#using-a-single-database
        pluginDivisionMode: schema
        connection:
          host: "${POSTGRES_HOST}"
          port: "${POSTGRES_PORT}"
          user: "${POSTGRES_USER}"
          password: "${POSTGRES_PASSWORD}"
      csp:
        script-src: ["'self'", "'unsafe-eval'", "'unsafe-inline'"]
        frame-src: ['*']
      # reading:
      #   allow:
      #   - host: demo.backstage.io
      #     path:
      #      - /api/graphql/schema
      auth:
        externalAccess:
          - type: static
            options:
              token: ${ADMIN_TOKEN}
              subject: admin-curl-access
        # dangerouslyDisableDefaultAuthPolicy: true
        # keys:
        #   # node -p 'require("crypto").randomBytes(24).toString("base64")'
        #   - secret: 8Dv1Q7xdGcUbG+nuBL9RwGRd6dSesaTA

    techdocs:
      builder: 'local'
      generator:
        runIn: 'local' # Alternatives - `docker` or 'local'
      publisher:
        type: 'local'

    permission:
      enabled: true
      # see https://github.com/backstage/community-plugins/tree/main/workspaces/rbac/plugins/rbac-backend
      rbac:
        maxDepth: 1
        admin:
          superUsers:
            - name: user:default/liuqs9751
            - name: user:default/wuzh
            - name: user:default/lir
            - name: group:default/admin
          users:
            - name: user:default/liuqs9751
            - name: user:default/wuzh
            - name: user:default/lir
            - name: group:default/admin
        pluginsWithPermission:
          - kubernetes
          - catalog
          - devops-console

    proxy:
      reviveConsumedRequestBodies: true
      endpoints:
        '/egress-api-service': 
          target: http://egress-api-service.fstest.svc/egress-api-service
          credentials: forward
          allowedHeaders:
          # Authorization 是 ID Token，不是 Access Token
          - x-forwarded-user
          - x-forwarded-preferred-username
          - x-forwarded-access-token
        '/fs-idp-helper': 
          target: http://egress-proxy-service.fstest.svc:50003

    auth:
      environment: production
      providers:
        oauth2Proxy:
          signIn:
            resolvers:
              - resolver: emailMatchingUserEntityProfileEmail
              # - resolver: emailLocalPartMatchingUserEntityName
              # - resolver: forwardedUserMatchingUserEntityName
    catalog:
      providers:
        keycloakOrg:
          default:
            baseUrl: https://iam.firstshare.cn
            loginRealm: devops
            realm: devops
            # client需要在Service Account Roles里配置查询user和group的权限
            clientId: api
            clientSecret: "LWGawVNNFqT7zf2TuxZrloMYDRQwgJ8H"
            schedule:
              frequency: { minutes: 15 }
              timeout: { minutes: 15 }
              initialDelay: { minutes: 2 }
      rules:
        - allow: [Template, Component, System, Domain, API, Resource, Location, User, Group]
      locations:
        - type: file
          target: /app/catalogs/firstshare/catalog-info.yaml

    kubernetes:
      frontend:
        podDelete:
          enabled: true
      serviceLocatorMethod:
        type: 'multiTenant'
      clusterLocatorMethods:
        - type: 'config'
          clusters:
            # - url: https://firstshare-k8s1.firstshare.cn:6443
            - url: https://kubernetes.default.svc
              name: k8s1
              title: 测试112集群
              authProvider: 'serviceAccount'
              skipTLSVerify: true
              skipMetricsLookup: false
              serviceAccountToken: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    explore:
      tools:
        - title: Dify
          description: 纷享私有化部署Dify，一个AI应用平台
          url: https://dify.firstshare.cn/
          image: https://dify.firstshare.cn/logo/logo.png
          tags:
            - ai
            - devops

postgresql:
  enabled: true
  image:
    registry: reg.firstshare.cn/docker.io
    repository: bitnami/postgresql
    tag: 17.2.0-debian-12-r10
  global:
    # imageRegistry: "reg.firstshare.cn/docker.io"
    storageClass: "managed-nfs-storage"
    security:
      allowInsecureImages: true
  architecture: standalone
  auth:
    username: backstage
    password: backstage.jVa7o
    postgresPassword: backstage.uU2Xh
    database: backstage
    secretKeys:
      adminPasswordKey: postgres-password
      userPasswordKey: password
      replicationPasswordKey: replication-password



